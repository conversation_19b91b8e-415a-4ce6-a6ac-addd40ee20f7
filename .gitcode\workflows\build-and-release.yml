name: Build and Release

on:
  push:
    branches: [ "main" ]
    tags:
      - 'v*'
  pull_request:
    branches: [ "main" ]

jobs:
  build:
    runs-on: euleros-2.10.1
    
    steps:
      - name: Checkout code
        uses: checkout-action@0.0.1
      
      - name: Setup Node.js
        uses: setup-node@0.0.1
        with:
          node-version: '20.10.0'
      
      - name: Install dependencies
        run: cd repo_workspace && npm ci
      
      - name: Generate Protocol Buffers
        run: cd repo_workspace && npm run generate
      
      - name: Build project
        run: cd repo_workspace && npm run build
      
      - name: Run tests
        run: cd repo_workspace && npm test
        continue-on-error: true
      
      - name: Create package
        run: cd repo_workspace && npm pack
      
      - name: Upload build artifacts
        if: startsWith(github.ref, 'refs/tags/')
        run: |
          cd repo_workspace
          echo "Package created: $(ls *.tgz)"
          echo "Build completed for tag: ${{ github.ref_name }}"

  release:
    needs: build
    runs-on: euleros-2.10.1
    if: startsWith(github.ref, 'refs/tags/')
    
    steps:
      - name: Checkout code
        uses: checkout-action@0.0.1
      
      - name: Setup Node.js
        uses: setup-node@0.0.1
        with:
          node-version: '20.10.0'
      
      - name: Install dependencies
        run: cd repo_workspace && npm ci
      
      - name: Generate and build
        run: |
          cd repo_workspace
          npm run generate
          npm run build
          npm pack
      
      - name: Prepare release info
        run: |
          cd repo_workspace
          echo "Release for tag: ${{ github.ref_name }}"
          echo "Package file: $(ls *.tgz)"
          echo "Release ready!"
