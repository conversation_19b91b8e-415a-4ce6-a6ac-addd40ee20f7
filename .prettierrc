{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "bracketSameLine": false, "overrides": [{"files": ["chatjs/*.js"], "options": {"printWidth": 120, "tabWidth": 2, "semi": true, "singleQuote": true, "trailingComma": "es5", "bracketSpacing": true, "arrowParens": "avoid"}}]}