syntax = "proto3";

package exa.seat_management_pb;

import "google/protobuf/timestamp.proto";

// 用户状态响应消息
message GetUserStatusResponse {
    UserStatus user_status = 1;    // 用户状态信息
    PlanInfo plan_info = 2;        // 计划信息
}


// 用户状态 - 包含用户的基本信息、权限和使用情况
message UserStatus {
    bool pro = 1;                                      // 是否为专业版用户
    bool disable_telemetry = 2;                        // 是否禁用遥测
    string name = 3;                                   // 用户名称
    bool ignore_chat_telemetry_setting = 4;            // 是否忽略聊天遥测设置
    string team_id = 5;                                // 团队ID
    UserTeamStatus team_status = 6;                    // 团队状态
    string email = 7;                                  // 用户邮箱
    repeated TeamsFeatures teams_features = 8;         // 团队功能列表
    repeated UserFeatures user_features = 9;           // 用户功能列表
    repeated Permission permissions = 11;              // 权限列表
    PlanInfo plan_info = 12;                          // 计划信息
    PlanStatus plan_status = 13;                      // 计划状态
    int64 user_used_prompt_credits = 28;              // 已使用的提示点数
    int64 user_used_flow_credits = 29;                // 已使用的流程点数
    bool has_fingerprint_set = 30;                    // 是否设置了指纹
    bool has_used_windsurf = 31;                      // 是否使用过Windsurf
    TeamConfig team_config = 32;              // 默认团队配置 (no: 32) update 2025-04-03
    CascadeModelConfigData cascade_model_config_data = 33; // 级联模型配置数据 (no: 33) update 2025-06-21 windsurf 1.11.0
}

// 计划状态
message PlanStatus {
    PlanInfo plan_info = 1;                           // 计划信息
    google.protobuf.Timestamp plan_start = 2;         // 计划开始时间
    google.protobuf.Timestamp plan_end = 3;           // 计划结束时间
    int32 available_flex_credits = 4;                 // 可用的弹性点数
    int32 used_flow_credits = 5;                      // 已使用的流程点数
    int32 used_prompt_credits = 6;                    // 已使用的提示点数
    int32 used_flex_credits = 7;                      // 已使用的弹性点数
    int32 available_prompt_credits = 8;               // 可用的提示点数
    int32 available_flow_credits = 9;                 // 可用的流程点数
}

// 计划信息 - 包含用户订阅计划的详细配置和限制
message PlanInfo {
    TeamsTier teams_tier = 1;                         // 团队等级
    string plan_name = 2;                             // 计划名称
    bool has_autocomplete_fast_mode = 3;              // 是否启用快速自动完成模式
    bool allow_sticky_premium_models = 4;             // 是否允许固定高级模型
    bool has_forge_access = 5;                        // 是否有Forge访问权限
    int64 max_num_premium_chat_messages = 6;          // 高级聊天消息数量上限
    int64 max_num_chat_input_tokens = 7;              // 聊天输入令牌数量上限
    int64 max_custom_chat_instruction_characters = 8;  // 自定义聊天指令字符数上限
    int64 max_num_pinned_context_items = 9;           // 固定上下文项数量上限
    int64 max_local_index_size = 10;                  // 本地索引大小上限
    bool disable_code_snippet_telemetry = 11;         // 是否禁用代码片段遥测
    int32 monthly_prompt_credits = 12;                // 每月提示点数
    int32 monthly_flow_credits = 13;                  // 每月流程点数
    int32 monthly_flex_credit_purchase_amount = 14;    // 每月弹性点数购买额度
    bool allow_premium_command_models = 15;           // 是否允许高级命令模型
    bool is_enterprise = 16;                          // 是否为企业版
    bool is_teams = 17;                               // 是否为团队版
    bool can_buy_more_credits = 18;                   // 是否可以购买更多点数
    bool cascade_web_search_enabled = 19;             // 是否启用级联网络搜索
    bool can_customize_app_icon = 20;                 // 是否可以自定义应用图标
    repeated AllowedModelConfig cascade_allowed_models_config = 21;  // 允许的级联模型配置列表
    bool cascade_can_auto_run_commands = 22;          // 是否允许级联自动运行命令
    bool has_tab_to_jump = 23;                        // 是否启用tab跳转功能
    TeamConfig default_team_config = 24;              // 默认团队配置 update 2025-04-03
    bool can_generate_commit_messages = 25;           // 是否可以生成提交消息 update 2025-04-03
    int32 max_unclaimed_sites = 26;                   // 最大未认领站点数 update 2025-06-21 windsurf 1.10.5
    // update 2025-05-12 windsurf 1.8.2
    bool knowledge_base_enabled = 27;                 // 是否启用知识库
    bool can_share_conversations = 28;                // 是否可以分享对话
    bool can_allow_cascade_in_background = 29;        // 是否可以允许级联在后台运行
    // update 2025-06-21 windsurf 1.10.5 新增字段
    map<int32, TeamFeatureConfig> default_team_features = 30;  // 默认团队功能配置映射
    bool browser_enabled = 31;                        // 是否启用浏览器功能
}

// 团队功能配置 - update 2025-06-21 windsurf 1.10.5
message TeamFeatureConfig {
    bool enabled = 1;                                  // 功能是否启用
    map<string, string> config_params = 2;            // 功能配置参数
    int64 usage_limit = 3;                            // 使用限制
    string description = 4;                           // 功能描述
}

// 级联模型配置数据 - update 2025-06-21 windsurf 1.11.0
message CascadeModelConfigData {
    repeated ClientModelConfig client_model_configs = 1;    // 客户端模型配置列表
    repeated ClientModelSort client_model_sorts = 2;        // 客户端模型排序列表
    DefaultOverrideModelConfig default_override_model_config = 3; // 默认覆盖模型配置 (可选)
}

// 客户端模型组 - update 2025-06-21 windsurf 1.11.0
message ClientModelGroup {
    string group_name = 1;                             // 组名称
    repeated string model_labels = 2;                  // 模型标签列表
}

// 客户端模型排序 - update 2025-06-21 windsurf 1.11.0
message ClientModelSort {
    string name = 1;                                   // 排序名称
    repeated ClientModelGroup groups = 2;              // 模型组列表
}

// 默认覆盖模型配置 - update 2025-06-21 windsurf 1.11.0
message DefaultOverrideModelConfig {
    ModelOrAlias model_or_alias = 1;                   // 模型或别名
    string version_id = 2;                             // 版本ID
}

// 团队配置
message TeamConfig {
    string team_id = 1;                                // 团队ID
    int64 user_prompt_credit_cap = 2;                  // 用户提示点数上限
    int64 user_flow_credit_cap = 3;                    // 用户流程点数上限
    bool auto_provision_cascade_seat = 4;              // 自动配置级联席位
    bool allow_mcp_servers = 5;                        // 允许MCP服务器
    bool allow_auto_run_commands = 7;                  // 允许自动运行命令
    bool allow_custom_recipes = 8;                     // 允许自定义配方
    int64 max_unclaimed_sites = 9;                     // 最大未认领站点数
    bool allow_app_deployments = 10;                   // 允许应用部署
    int64 max_new_sites_per_day = 11;                  // 每日最大新站点数
}

// 允许的模型配置
message AllowedModelConfig {
    ModelOrAlias model_or_alias = 1;                  // 模型或别名
    float credit_multiplier = 2;                      // 点数乘数
}


// 模型或别名选择
message ModelOrAlias {
    oneof choice {
        Model model = 1;                              // 模型
        Alias alias = 2;                              // 别名
    }
}

// 模型枚举
enum Model {
    MODEL_UNSPECIFIED = 0;                            // 未指定模型
    // 仅包含必要的值，完整定义在model_config.proto中
    // Claude 模型 - update 2025-06-21 windsurf 1.11.0
    MODEL_CLAUDE_3_7_SONNET_20250219 = 226;          // Claude 3.7 Sonnet
    MODEL_CLAUDE_4_OPUS = 290;                        // Claude 4.0 Opus
    MODEL_CLAUDE_4_SONNET = 281;                      // Claude 4.0 Sonnet
    // Google 模型 - update 2025-06-21 windsurf 1.11.0
    MODEL_GOOGLE_GEMINI_1_0_PRO = 61;                 // Google Gemini 1.0 Pro
    MODEL_GOOGLE_GEMINI_1_5_PRO = 62;                 // Google Gemini 1.5 Pro
    MODEL_GOOGLE_GEMINI_2_0_FLASH = 184;              // Google Gemini 2.0 Flash
    MODEL_GOOGLE_GEMINI_2_5_PRO = 246;                // Google Gemini 2.5 Pro
    MODEL_GOOGLE_GEMINI_2_5_FLASH = 312;              // Google Gemini 2.5 Flash
    MODEL_GOOGLE_GEMINI_2_5_FLASH_THINKING = 313;     // Google Gemini 2.5 Flash Thinking
    MODEL_GOOGLE_GEMINI_EXP_1206 = 183;               // Google Gemini Exp 1206
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20 = 275; // Google Gemini 2.5 Flash Preview 05-20
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING = 276; // Google Gemini 2.5 Flash Preview 05-20 Thinking
    // DeepSeek 模型 - update 2025-06-21 windsurf 1.11.0
    MODEL_DEEPSEEK_V3 = 205;                          // DeepSeek V3
    MODEL_DEEPSEEK_R1 = 206;                          // DeepSeek R1
    MODEL_DEEPSEEK_R1_FAST = 216;                     // DeepSeek R1 Fast
    MODEL_DEEPSEEK_R1_SLOW = 215;                     // DeepSeek R1 Slow
    // OpenAI 模型 - update 2025-06-21 windsurf 1.11.0
    MODEL_O3_PRO_2025_06_10 = 294;                    // OpenAI O3 Pro 2025-06-10
    MODEL_O3_PRO_2025_06_10_LOW = 295;                // OpenAI O3 Pro 2025-06-10 Low
    MODEL_O3_PRO_2025_06_10_HIGH = 296;               // OpenAI O3 Pro 2025-06-10 High
    // 新增模型 - update 2025-06-21 windsurf 1.12.1
    MODEL_KIMI_K2 = 323;                              // Kimi K2
    MODEL_QWEN_3_235B_INSTRUCT = 324;                 // Qwen 3 235B Instruct
    MODEL_QWEN_3_CODER_480B_INSTRUCT = 325;           // Qwen 3 Coder 480B Instruct
    MODEL_GPT_OSS_120B = 326;                         // GPT OSS 120B
    MODEL_QWEN_3_CODER_480B_INSTRUCT_FAST = 327;      // Qwen 3 Coder 480B Instruct Fast
    MODEL_CLAUDE_4_1_OPUS = 328;                      // Claude 4.1 Opus
    MODEL_CLAUDE_4_1_OPUS_THINKING = 329;             // Claude 4.1 Opus Thinking
    MODEL_GPT_5_NANO = 337;                           // GPT-5 Nano
    MODEL_CHAT_GPT_5_MINIMAL = 338;                   // GPT-5 Minimal
    MODEL_CHAT_GPT_5_LOW = 339;                       // GPT-5 Low Reasoning
    MODEL_CHAT_GPT_5 = 340;                           // GPT-5 Standard
    MODEL_CHAT_GPT_5_HIGH = 341;                      // GPT-5 High Reasoning
}

// 别名枚举
enum Alias {
    ALIAS_UNSPECIFIED = 0;                            // 未指定别名
    MODEL_ALIAS_CASCADE_BASE = 1;                     // Cascade Base
}

// 用户团队状态
enum UserTeamStatus {
    USER_TEAM_STATUS_UNSPECIFIED = 0;                 // 未指定状态
    USER_TEAM_STATUS_PENDING = 1;                     // 待定状态
    USER_TEAM_STATUS_APPROVED = 2;                    // 已批准状态
    USER_TEAM_STATUS_REJECTED = 3;                    // 已拒绝状态
}

// 团队等级
enum TeamsTier {
    TEAMS_TIER_UNSPECIFIED = 0;                       // 未指定等级
    TEAMS_TIER_TEAMS = 1;                             // 团队版
    TEAMS_TIER_PRO = 2;                               // 专业版
    TEAMS_TIER_TRIAL = 9;                             // 试用版
    TEAMS_TIER_ENTERPRISE_SAAS = 3;                   // 企业SaaS版
    TEAMS_TIER_HYBRID = 4;                            // 混合版
    TEAMS_TIER_ENTERPRISE_SELF_HOSTED = 5;            // 企业自托管版
    TEAMS_TIER_WAITLIST_PRO = 6;                      // 专业版等待列表
    TEAMS_TIER_TEAMS_ULTIMATE = 7;                    // 团队旗舰版
    TEAMS_TIER_PRO_ULTIMATE = 8;                      // 专业旗舰版
}

// 用户特性
enum UserFeatures {
    USER_FEATURES_UNSPECIFIED = 0;                    // 未指定特性
    USER_FEATURES_CORTEX = 1;                         // Cortex特性
    USER_FEATURES_CORTEX_TEST = 2;                    // Cortex测试特性
}

// 团队特性
enum TeamsFeatures {
    TEAMS_FEATURES_UNSPECIFIED = 0;                   // 未指定特性
    TEAMS_FEATURES_SSO = 1;                           // 单点登录
    TEAMS_FEATURES_ATTRIBUTION = 2;                    // 归因功能
    TEAMS_FEATURES_PHI = 3;                           // PHI功能
    TEAMS_FEATURES_CORTEX = 4;                        // Cortex功能
    TEAMS_FEATURES_OPENAI_DISABLED = 5;               // OpenAI禁用
    TEAMS_FEATURES_REMOTE_INDEXING_DISABLED = 6;      // 远程索引禁用
    TEAMS_FEATURES_API_KEY_ENABLED = 7;               // API密钥启用
}

// 权限
enum Permission {
    PERMISSION_UNSPECIFIED = 0;                       // 未指定权限
    PERMISSION_ATTRIBUTION_READ = 1;                   // 归因读取权限
    PERMISSION_ANALYTICS_READ = 2;                     // 分析读取权限
    PERMISSION_LICENSE_READ = 3;                       // 许可证读取权限
    PERMISSION_TEAM_USER_READ = 4;                     // 团队用户读取权限
    PERMISSION_TEAM_USER_UPDATE = 5;                   // 团队用户更新权限
    PERMISSION_TEAM_USER_DELETE = 6;                   // 团队用户删除权限
    PERMISSION_TEAM_USER_INVITE = 17;                  // 团队用户邀请权限
    PERMISSION_INDEXING_READ = 7;                      // 索引读取权限
    PERMISSION_INDEXING_CREATE = 8;                    // 索引创建权限
    PERMISSION_INDEXING_UPDATE = 9;                    // 索引更新权限
    PERMISSION_INDEXING_DELETE = 10;                   // 索引删除权限
    PERMISSION_INDEXING_MANAGEMENT = 27;               // 索引管理权限
    PERMISSION_FINETUNING_READ = 19;                  // 微调读取权限
    PERMISSION_FINETUNING_CREATE = 20;                // 微调创建权限
    PERMISSION_FINETUNING_UPDATE = 21;                // 微调更新权限
    PERMISSION_FINETUNING_DELETE = 22;                // 微调删除权限
    PERMISSION_SSO_READ = 11;                         // SSO读取权限
    PERMISSION_SSO_WRITE = 12;                        // SSO写入权限
    PERMISSION_SERVICE_KEY_READ = 13;                  // 服务密钥读取权限
    PERMISSION_SERVICE_KEY_CREATE = 14;                // 服务密钥创建权限
    PERMISSION_SERVICE_KEY_UPDATE = 28;                // 服务密钥更新权限
    PERMISSION_SERVICE_KEY_DELETE = 15;                // 服务密钥删除权限
    PERMISSION_ROLE_READ = 23;                         // 角色读取权限
    PERMISSION_ROLE_CREATE = 24;                       // 角色创建权限
    PERMISSION_ROLE_UPDATE = 25;                       // 角色更新权限
    PERMISSION_ROLE_DELETE = 26;                       // 角色删除权限
    PERMISSION_BILLING_READ = 16;                      // 账单读取权限
    PERMISSION_BILLING_WRITE = 18;                     // 账单写入权限
    PERMISSION_EXTERNAL_CHAT_UPDATE = 29;              // 外部聊天更新权限
    PERMISSION_TEAM_SETTINGS_READ = 30;                // 团队设置读取权限
    PERMISSION_TEAM_SETTINGS_UPDATE = 31;              // 团队设置更新权限
}

// 模型定价类型枚举 - update 2025-06-21 windsurf 1.11.0
enum ModelPricingType {
    MODEL_PRICING_TYPE_UNSPECIFIED = 0;              // 未指定定价类型
    MODEL_PRICING_TYPE_STATIC_CREDIT = 1;            // 静态点数
    MODEL_PRICING_TYPE_API = 2;                      // API 定价
    MODEL_PRICING_TYPE_BYOK = 3;                     // 自带密钥 (Bring Your Own Key)
}

// 提供商枚举 - update 2025-06-21 windsurf 1.11.0
enum Provider {
    MODEL_PROVIDER_UNSPECIFIED = 0;                  // 未指定提供商
    MODEL_PROVIDER_ANTHROPIC = 1;                    // Anthropic
    MODEL_PROVIDER_OPENAI = 2;                       // OpenAI
    MODEL_PROVIDER_GOOGLE = 3;                       // Google
    MODEL_PROVIDER_DEEPSEEK = 4;                     // DeepSeek
    // 新增提供商 - update 2025-06-21 windsurf 1.12.1
    MODEL_PROVIDER_QWEN = 5;                         // Qwen
    MODEL_PROVIDER_KIMI = 6;                         // Kimi
}

// 客户端模型配置 - update 2025-06-21 windsurf 1.11.0
message ClientModelConfig {
    string label = 1;                                 // 模型标签
    ModelOrAlias model_or_alias = 2;                  // 模型或别名
    float credit_multiplier = 3;                      // 点数乘数
    ModelPricingType pricing_type = 13;               // 定价类型 (no: 13)
    bool disabled = 4;                                // 是否禁用
    bool supports_images = 5;                         // 是否支持图像
    bool supports_legacy = 6;                         // 是否支持旧版
    bool is_premium = 7;                              // 是否为高级版
    string beta_warning_message = 8;                  // Beta 警告消息
    bool is_beta = 9;                                 // 是否为 Beta 版
    Provider provider = 10;                           // 提供商
    bool is_recommended = 11;                         // 是否推荐
    repeated TeamsTier allowed_tiers = 12;            // 允许的等级列表
}
