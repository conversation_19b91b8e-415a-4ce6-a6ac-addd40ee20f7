name: Test and Lint

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main" ]

jobs:
  test:
    runs-on: euleros-2.10.1
    
    steps:
      - name: Checkout code
        uses: checkout-action@0.0.1
      
      - name: Setup Node.js
        uses: setup-node@0.0.1
        with:
          node-version: '20.10.0'
      
      - name: Install dependencies
        run: cd repo_workspace && npm ci
      
      - name: Check TypeScript compilation
        run: cd repo_workspace && npm run build
        continue-on-error: true
      
      - name: Run tests (if available)
        run: cd repo_workspace && npm test
        continue-on-error: true
      
      - name: Check code formatting
        run: |
          cd repo_workspace
          echo "Checking code formatting..."
          # 如果有 prettier，可以运行格式检查
          # npx prettier --check "src/**/*.{ts,js}"
          echo "Code check completed"
