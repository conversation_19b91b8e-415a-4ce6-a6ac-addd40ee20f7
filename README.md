# whistle.windsurf-modify-userstatus

一个用于拦截和修改 Windsurf (windsurf.ai) 请求的 whistle 插件。它可以修改用户状态、自定义模型配置并阻止遥测/分析请求以增强隐私和功能体验。

## 功能特点

### 核心功能

- **用户状态增强**：修改用户状态响应，启用 Pro Ultimate 版本的全部高级功能

  - 无限制的提示点数和流程点数
  - 启用高级模型访问权限
  - 自动配置级联席位

- **模型配置定制**：修改命令模型配置响应 (CommandModelConfigs)
  - 自定义可用的高级模型列表
  - 当前支持的高级模型：
    - Claude 3.5 Sonnet
    - Claude 3.7 Sonnet
    - Gemini 2.5 Pro
  - 可在源码中轻松添加更多模型

### 隐私保护

- **遥测阻断**：拦截并阻止所有遥测和分析请求
  - 阻止 RecordAnalyticsEvent 请求
  - 阻止 RecordCascadeUsage 请求
  - 阻止 RecordCommandUsage 请求
  - 阻止 RecordCompletions 请求
  - 以及其他所有遥测端点

### 技术特性

- 基于 whistle 代理框架，易于配置和使用
- 使用 Protocol Buffers 处理数据，确保格式兼容性
- 支持 CORS 请求处理，解决跨域问题
- TypeScript 编写，代码结构清晰

## 安装

### 前置条件

- 安装 Node.js (推荐 v16 或更高版本)
- 安装 whistle：`npm install -g whistle`
- 可选：安装 Proxifier (用于精确代理控制)

### 插件安装

下载 tgz 文件，然后使用 npm 全局安装：

```bash
npm install -g ./whistle.windsurf-modify-userstatus-1.9.0.tgz
```

或者从源码安装：

```bash
git clone https://gitcode.com/yongchang/whistle.windsurf-modify-userstatus.git
cd whistle.windsurf-modify-userstatus
npm install
npm run build
npm link
```

## 使用方法

### 基本配置

1. 启动 whistle：

   ```bash
   w2 start
   ```

2. 访问 whistle 管理界面：http://127.0.0.1:8899/

3. 安装 whistle 根证书（首次使用时）：
   - 在 whistle 管理界面点击 HTTPS 菜单
   - 点击「Download RootCA」下载并安装证书
   - 确保证书被系统信任

### 高级配置（推荐）

使用 Proxifier 进行精确代理控制：

1. 在 Proxifier 中添加代理服务器：

   - 地址：127.0.0.1
   - 端口：8899（whistle 默认端口）
   - 协议：HTTPS

2. 添加代理规则：
   - 仅将 `language_server_windows_x64.exe` 进程的流量定向到 whistle 代理
   - 其他程序保持直连

这种方式可以最小化对系统其他应用的影响，只代理 Windsurf 的流量。

## 工作原理

本插件通过拦截 Windsurf 与服务器之间的通信，修改关键请求和响应：

1. 拦截 GetUserStatus 请求，返回具有高级权限的用户状态
2. 拦截 GetCommandModelConfigs 请求，提供自定义的模型配置
3. 阻止所有遥测和分析请求，保护用户隐私

所有修改都在本地进行，不会影响 Windsurf 服务器或其他用户。

## 常见问题

**Q: 插件安装后没有效果？**  
A: 请确保证书正确安装，并且 Windsurf 的流量确实经过了 whistle 代理。可以在 whistle 的 Network 面板中查看是否有相关请求。

**Q: 如何验证插件是否正常工作？**  
A: 在 whistle 的日志中应该能看到类似 "🛡️ intercept and modify GetUserStatus response!" 的信息。同时，Windsurf 界面应该显示您拥有高级权限。

## 注意事项

本插件仅用于学习和研究 Protocol Buffers 和代理技术，请勿用于商业用途。使用本插件可能违反 Windsurf 的服务条款，请自行承担风险。未经授权的滥用概不负责。

## 许可证

MIT
