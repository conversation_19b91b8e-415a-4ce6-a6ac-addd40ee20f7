{"editor.gotoLocation.alternativeDeclarationCommand": "editor.action.revealDefinition", "editor.gotoLocation.alternativeDefinitionCommand": "editor.action.revealDefinition", "editor.gotoLocation.alternativeTypeDefinitionCommand": "editor.action.revealDefinition", "editor.selectionHighlight": false, "files.autoSave": "onFocusChange", "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.quickSuggestions": {"other": "on", "comments": "off", "strings": "on"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": true, "prettier.configPath": ".prettier<PERSON>", "prettier.ignorePath": ".prettieri<PERSON>re", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "never"}}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "files.exclude": {"**/chatjs": false}, "search.exclude": {"**/chatjs": false}, "typescript.preferences.exclude": ["**/chatjs/**"], "javascript.preferences.exclude": ["**/chatjs/**"], "typescript.suggest.autoImports": "off", "javascript.suggest.autoImports": "off", "typescript.preferences.includePackageJsonAutoImports": "off", "javascript.preferences.includePackageJsonAutoImports": "off", "typescript.suggest.includeAutomaticOptionalChainCompletions": false, "javascript.suggest.includeAutomaticOptionalChainCompletions": false, "eslint.workingDirectories": [{"directory": "./src", "changeProcessCwd": true}], "eslint.exclude": ["**/chatjs/**"], "typescript.validate.enable": true, "javascript.validate.enable": true, "typescript.preferences.disableSuggestions": false, "javascript.preferences.disableSuggestions": false, "files.watcherExclude": {"**/chatjs/**": true}, "typescript.disableAutomaticTypeAcquisition": false, "javascript.implicitProjectConfig.checkJs": false, "typescript.suggest.enabled": true, "javascript.suggest.enabled": true, "files.associations": {"chatjs/*.js": "javascript"}}