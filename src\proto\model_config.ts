// @generated by protobuf-ts 2.10.0
// @generated from protobuf file "proto/model_config.proto" (package "exa.codeium_common_pb", syntax proto3)
// tslint:disable
import type { BinaryWriteOptions } from "@protobuf-ts/runtime";
import type { IBinaryWriter } from "@protobuf-ts/runtime";
import { WireType } from "@protobuf-ts/runtime";
import type { BinaryReadOptions } from "@protobuf-ts/runtime";
import type { IBinaryReader } from "@protobuf-ts/runtime";
import { UnknownFieldHandler } from "@protobuf-ts/runtime";
import type { PartialMessage } from "@protobuf-ts/runtime";
import { reflectionMergePartial } from "@protobuf-ts/runtime";
import { MessageType } from "@protobuf-ts/runtime";
/**
 * 级联模型配置响应
 *
 * @generated from protobuf message exa.codeium_common_pb.GetCascadeModelConfigsResponse
 */
export interface GetCascadeModelConfigsResponse {
    /**
     * @generated from protobuf field: repeated exa.codeium_common_pb.ClientModelConfig client_model_configs = 1;
     */
    clientModelConfigs: ClientModelConfig[]; // 客户端模型配置列表
}
/**
 * command 模型配置响应
 *
 * @generated from protobuf message exa.codeium_common_pb.GetCommandModelConfigsResponse
 */
export interface GetCommandModelConfigsResponse {
    /**
     * @generated from protobuf field: repeated exa.codeium_common_pb.ClientModelConfig client_model_configs = 1;
     */
    clientModelConfigs: ClientModelConfig[]; // 客户端模型配置列表
}
/**
 * 客户端模型配置
 *
 * @generated from protobuf message exa.codeium_common_pb.ClientModelConfig
 */
export interface ClientModelConfig {
    /**
     * @generated from protobuf field: string label = 1;
     */
    label: string; // 标签名称
    /**
     * @generated from protobuf field: exa.codeium_common_pb.ModelOrAlias model_or_alias = 2;
     */
    modelOrAlias?: ModelOrAlias; // 模型或别名
    /**
     * @generated from protobuf field: fixed32 credit_multiplier = 3;
     */
    creditMultiplier: number; // 信用点数乘数
    /**
     * @generated from protobuf field: bool disabled = 4;
     */
    disabled: boolean; // 是否禁用
    /**
     * @generated from protobuf field: bool supports_images = 5;
     */
    supportsImages: boolean; // 是否支持图片
    /**
     * @generated from protobuf field: bool supports_legacy = 6;
     */
    supportsLegacy: boolean; // 是否支持旧版本
    /**
     * @generated from protobuf field: bool is_premium = 7;
     */
    isPremium: boolean; // 是否为高级版本
    /**
     * @generated from protobuf field: string beta_warning_message = 8;
     */
    betaWarningMessage: string; // Beta 版警告消息
    /**
     * @generated from protobuf field: bool is_beta = 9;
     */
    isBeta: boolean; // 是否为 Beta 版
    /**
     * @generated from protobuf field: exa.codeium_common_pb.Provider provider = 10;
     */
    provider: Provider; // 提供商
    /**
     * @generated from protobuf field: bool is_recommended = 11;
     */
    isRecommended: boolean; // 是否推荐
    /**
     * @generated from protobuf field: repeated exa.codeium_common_pb.Tier allowed_tiers = 12;
     */
    allowedTiers: Tier[]; // 允许的等级
}
/**
 * 模型或别名选择
 *
 * @generated from protobuf message exa.codeium_common_pb.ModelOrAlias
 */
export interface ModelOrAlias {
    /**
     * @generated from protobuf oneof: choice
     */
    choice: {
        oneofKind: "model";
        /**
         * @generated from protobuf field: exa.codeium_common_pb.Model model = 1;
         */
        model: Model; // 模型
    } | {
        oneofKind: "alias";
        /**
         * @generated from protobuf field: exa.codeium_common_pb.Alias alias = 2;
         */
        alias: Alias; // 别名
    } | {
        oneofKind: undefined;
    };
}
/**
 * @generated from protobuf enum exa.codeium_common_pb.Model
 */
export enum Model {
    /**
     * 未指定模型
     *
     * @generated from protobuf enum value: MODEL_UNSPECIFIED = 0;
     */
    MODEL_UNSPECIFIED = 0,
    /**
     * 嵌入式模型
     *
     * @generated from protobuf enum value: MODEL_EMBED_6591 = 20;
     */
    MODEL_EMBED_6591 = 20,
    /**
     * 模型 8341
     *
     * @generated from protobuf enum value: MODEL_8341 = 33;
     */
    MODEL_8341 = 33,
    /**
     * 模型 8528
     *
     * @generated from protobuf enum value: MODEL_8528 = 42;
     */
    MODEL_8528 = 42,
    /**
     * 模型 9024
     *
     * @generated from protobuf enum value: MODEL_9024 = 41;
     */
    MODEL_9024 = 41,
    /**
     * 模型 14602
     *
     * @generated from protobuf enum value: MODEL_14602 = 112;
     */
    MODEL_14602 = 112,
    /**
     * 模型 15133
     *
     * @generated from protobuf enum value: MODEL_15133 = 115;
     */
    MODEL_15133 = 115,
    /**
     * 模型 15302
     *
     * @generated from protobuf enum value: MODEL_15302 = 119;
     */
    MODEL_15302 = 119,
    /**
     * 模型 15335
     *
     * @generated from protobuf enum value: MODEL_15335 = 121;
     */
    MODEL_15335 = 121,
    /**
     * 模型 15336
     *
     * @generated from protobuf enum value: MODEL_15336 = 122;
     */
    MODEL_15336 = 122,
    /**
     * 模型 15931
     *
     * @generated from protobuf enum value: MODEL_15931 = 167;
     */
    MODEL_15931 = 167,
    /**
     * 查询模型 9905
     *
     * @generated from protobuf enum value: MODEL_QUERY_9905 = 48;
     */
    MODEL_QUERY_9905 = 48,
    /**
     * 查询模型 11791
     *
     * @generated from protobuf enum value: MODEL_QUERY_11791 = 66;
     */
    MODEL_QUERY_11791 = 66,
    /**
     * 聊天模型 11120
     *
     * @generated from protobuf enum value: MODEL_CHAT_11120 = 57;
     */
    MODEL_CHAT_11120 = 57,
    /**
     * 聊天模型 11121
     *
     * @generated from protobuf enum value: MODEL_CHAT_11121 = 58;
     */
    MODEL_CHAT_11121 = 58,
    /**
     * 聊天模型 12119
     *
     * @generated from protobuf enum value: MODEL_CHAT_12119 = 70;
     */
    MODEL_CHAT_12119 = 70,
    /**
     * 聊天模型 12121
     *
     * @generated from protobuf enum value: MODEL_CHAT_12121 = 69;
     */
    MODEL_CHAT_12121 = 69,
    /**
     * 聊天模型 12437
     *
     * @generated from protobuf enum value: MODEL_CHAT_12437 = 74;
     */
    MODEL_CHAT_12437 = 74,
    /**
     * 聊天模型 12491
     *
     * @generated from protobuf enum value: MODEL_CHAT_12491 = 76;
     */
    MODEL_CHAT_12491 = 76,
    /**
     * 聊天模型 12623
     *
     * @generated from protobuf enum value: MODEL_CHAT_12623 = 78;
     */
    MODEL_CHAT_12623 = 78,
    /**
     * 聊天模型 12950
     *
     * @generated from protobuf enum value: MODEL_CHAT_12950 = 79;
     */
    MODEL_CHAT_12950 = 79,
    /**
     * 聊天模型 12968
     *
     * @generated from protobuf enum value: MODEL_CHAT_12968 = 101;
     */
    MODEL_CHAT_12968 = 101,
    /**
     * 聊天模型 13404
     *
     * @generated from protobuf enum value: MODEL_CHAT_13404 = 102;
     */
    MODEL_CHAT_13404 = 102,
    /**
     * 聊天模型 13566
     *
     * @generated from protobuf enum value: MODEL_CHAT_13566 = 103;
     */
    MODEL_CHAT_13566 = 103,
    /**
     * 聊天模型 13930
     *
     * @generated from protobuf enum value: MODEL_CHAT_13930 = 108;
     */
    MODEL_CHAT_13930 = 108,
    /**
     * 聊天模型 14255
     *
     * @generated from protobuf enum value: MODEL_CHAT_14255 = 110;
     */
    MODEL_CHAT_14255 = 110,
    /**
     * 聊天模型 14256
     *
     * @generated from protobuf enum value: MODEL_CHAT_14256 = 111;
     */
    MODEL_CHAT_14256 = 111,
    /**
     * 聊天模型 14942
     *
     * @generated from protobuf enum value: MODEL_CHAT_14942 = 114;
     */
    MODEL_CHAT_14942 = 114,
    /**
     * 聊天模型 15305
     *
     * @generated from protobuf enum value: MODEL_CHAT_15305 = 120;
     */
    MODEL_CHAT_15305 = 120,
    /**
     * 聊天模型 15600
     *
     * @generated from protobuf enum value: MODEL_CHAT_15600 = 123;
     */
    MODEL_CHAT_15600 = 123,
    /**
     * 聊天模型 16801
     *
     * @generated from protobuf enum value: MODEL_CHAT_16801 = 124;
     */
    MODEL_CHAT_16801 = 124,
    /**
     * 聊天模型 16718
     *
     * @generated from protobuf enum value: MODEL_CHAT_16718 = 175;
     */
    MODEL_CHAT_16718 = 175,
    /**
     * 聊天模型 15729
     *
     * @generated from protobuf enum value: MODEL_CHAT_15729 = 168;
     */
    MODEL_CHAT_15729 = 168,
    /**
     * 聊天模型 16579
     *
     * @generated from protobuf enum value: MODEL_CHAT_16579 = 173;
     */
    MODEL_CHAT_16579 = 173,
    /**
     * 聊天模型 16579_CRUSOE
     *
     * @generated from protobuf enum value: MODEL_CHAT_16579_CRUSOE = 174;
     */
    MODEL_CHAT_16579_CRUSOE = 174,
    /**
     * 聊天模型 18805
     *
     * @generated from protobuf enum value: MODEL_CHAT_18805 = 181;
     */
    MODEL_CHAT_18805 = 181,
    /**
     * 聊天模型 18468
     *
     * @generated from protobuf enum value: MODEL_CHAT_18468 = 210;
     */
    MODEL_CHAT_18468 = 210,
    /**
     * 聊天模型 19484
     *
     * @generated from protobuf enum value: MODEL_CHAT_19484 = 233;
     */
    MODEL_CHAT_19484 = 233,
    /**
     * 聊天模型 20706
     *
     * @generated from protobuf enum value: MODEL_CHAT_20706 = 235;
     */
    MODEL_CHAT_20706 = 235,
    /**
     * 聊天模型 21779
     *
     * @generated from protobuf enum value: MODEL_CHAT_21779 = 245;
     */
    MODEL_CHAT_21779 = 245,
    /**
     * 聊天模型 19040
     *
     * @generated from protobuf enum value: MODEL_CHAT_19040 = 211;
     */
    MODEL_CHAT_19040 = 211,
    /**
     * 聊天模型 19820
     *
     * @generated from protobuf enum value: MODEL_CHAT_19820 = 229;
     */
    MODEL_CHAT_19820 = 229,
    /**
     * 聊天模型 19821
     *
     * @generated from protobuf enum value: MODEL_CHAT_19821 = 230;
     */
    MODEL_CHAT_19821 = 230,
    /**
     * 聊天模型 19821_CRUSOE
     *
     * @generated from protobuf enum value: MODEL_CHAT_19821_CRUSOE = 244;
     */
    MODEL_CHAT_19821_CRUSOE = 244,
    /**
     * 聊天模型 23310
     *
     * @generated from protobuf enum value: MODEL_CHAT_23310 = 269;
     */
    MODEL_CHAT_23310 = 269,
    /**
     * 聊天模型 19822
     *
     * @generated from protobuf enum value: MODEL_CHAT_19822 = 231;
     */
    MODEL_CHAT_19822 = 231,
    /**
     * 聊天模型 22798
     *
     * @generated from protobuf enum value: MODEL_CHAT_22798 = 255;
     */
    MODEL_CHAT_22798 = 255,
    /**
     * 聊天模型 22799
     *
     * @generated from protobuf enum value: MODEL_CHAT_22799 = 256;
     */
    MODEL_CHAT_22799 = 256,
    /**
     * 聊天模型 22800
     *
     * @generated from protobuf enum value: MODEL_CHAT_22800 = 257;
     */
    MODEL_CHAT_22800 = 257,
    /**
     * 聊天模型 23151
     *
     * @generated from protobuf enum value: MODEL_CHAT_23151 = 267;
     */
    MODEL_CHAT_23151 = 267,
    /**
     * 聊天模型 23152
     *
     * @generated from protobuf enum value: MODEL_CHAT_23152 = 268;
     */
    MODEL_CHAT_23152 = 268,
    /**
     * 级联模型 22893
     *
     * @generated from protobuf enum value: MODEL_CASCADE_22893 = 270;
     */
    MODEL_CASCADE_22893 = 270,
    /**
     * 级联模型 20064
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20064 = 225;
     */
    MODEL_CASCADE_20064 = 225,
    /**
     * 级联模型 20065
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20065 = 236;
     */
    MODEL_CASCADE_20065 = 236,
    /**
     * 级联模型 20066
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20066 = 237;
     */
    MODEL_CASCADE_20066 = 237,
    /**
     * 级联模型 20067
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20067 = 238;
     */
    MODEL_CASCADE_20067 = 238,
    /**
     * 级联模型 20068
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20068 = 239;
     */
    MODEL_CASCADE_20068 = 239,
    /**
     * 级联模型 20069
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20069 = 240;
     */
    MODEL_CASCADE_20069 = 240,
    /**
     * 级联模型 20070
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20070 = 250;
     */
    MODEL_CASCADE_20070 = 250,
    /**
     * 级联模型 20071
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20071 = 251;
     */
    MODEL_CASCADE_20071 = 251,
    /**
     * 级联模型 20072
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20072 = 252;
     */
    MODEL_CASCADE_20072 = 252,
    /**
     * 级联模型 20073
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20073 = 253;
     */
    MODEL_CASCADE_20073 = 253,
    /**
     * 级联模型 20074
     *
     * @generated from protobuf enum value: MODEL_CASCADE_20074 = 254;
     */
    MODEL_CASCADE_20074 = 254,
    /**
     * 深度寻求 V3 内部
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_V3_INTERNAL = 247;
     */
    MODEL_DEEPSEEK_V3_INTERNAL = 247,
    /**
     * 深度寻求 V3 0324 内部
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_V3_0324_INTERNAL = 248;
     */
    MODEL_DEEPSEEK_V3_0324_INTERNAL = 248,
    /**
     * 深度寻求 R1 内部
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_R1_INTERNAL = 249;
     */
    MODEL_DEEPSEEK_R1_INTERNAL = 249,
    /**
     * Anthropic Windsurf Research
     *
     * @generated from protobuf enum value: MODEL_ANTHROPIC_WINDSURF_RESEARCH = 241;
     */
    MODEL_ANTHROPIC_WINDSURF_RESEARCH = 241,
    /**
     * Anthropic Windsurf Research Thinking
     *
     * @generated from protobuf enum value: MODEL_ANTHROPIC_WINDSURF_RESEARCH_THINKING = 242;
     */
    MODEL_ANTHROPIC_WINDSURF_RESEARCH_THINKING = 242,
    /**
     * 换代 11408
     *
     * @generated from protobuf enum value: MODEL_DRAFT_11408 = 65;
     */
    MODEL_DRAFT_11408 = 65,
    /**
     * 换代聊天 11883
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_11883 = 67;
     */
    MODEL_DRAFT_CHAT_11883 = 67,
    /**
     * 换代聊天 12196
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_12196 = 72;
     */
    MODEL_DRAFT_CHAT_12196 = 72,
    /**
     * 换代聊天 12413
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_12413 = 73;
     */
    MODEL_DRAFT_CHAT_12413 = 73,
    /**
     * 换代聊天 13175
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_13175 = 104;
     */
    MODEL_DRAFT_CHAT_13175 = 104,
    /**
     * 换代聊天 19823
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_19823 = 232;
     */
    MODEL_DRAFT_CHAT_19823 = 232,
    /**
     * 换代聊天 20707
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_20707 = 243;
     */
    MODEL_DRAFT_CHAT_20707 = 243,
    /**
     * 换代聊天 22801
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_22801 = 258;
     */
    MODEL_DRAFT_CHAT_22801 = 258,
    /**
     * 换代聊天 23508
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CHAT_23508 = 273;
     */
    MODEL_DRAFT_CHAT_23508 = 273,
    /**
     * 换代级联 23672
     *
     * @generated from protobuf enum value: MODEL_DRAFT_CASCADE_23672 = 274;
     */
    MODEL_DRAFT_CASCADE_23672 = 274,
    /**
     * 聊天 3.5 Turbo
     *
     * @generated from protobuf enum value: MODEL_CHAT_3_5_TURBO = 28;
     */
    MODEL_CHAT_3_5_TURBO = 28,
    /**
     * 聊天 GPT 4
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4 = 30;
     */
    MODEL_CHAT_GPT_4 = 30,
    /**
     * 聊天 GPT 4 1106 预览
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4_1106_PREVIEW = 37;
     */
    MODEL_CHAT_GPT_4_1106_PREVIEW = 37,
    /**
     * 文本嵌入 OpenAI Ada
     *
     * @generated from protobuf enum value: MODEL_TEXT_EMBEDDING_OPENAI_ADA = 91;
     */
    MODEL_TEXT_EMBEDDING_OPENAI_ADA = 91,
    /**
     * 文本嵌入 OpenAI 3 小
     *
     * @generated from protobuf enum value: MODEL_TEXT_EMBEDDING_OPENAI_3_SMALL = 163;
     */
    MODEL_TEXT_EMBEDDING_OPENAI_3_SMALL = 163,
    /**
     * 文本嵌入 OpenAI 3 大
     *
     * @generated from protobuf enum value: MODEL_TEXT_EMBEDDING_OPENAI_3_LARGE = 164;
     */
    MODEL_TEXT_EMBEDDING_OPENAI_3_LARGE = 164,
    /**
     * 聊天 GPT 4O 2024 05 13
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4O_2024_05_13 = 71;
     */
    MODEL_CHAT_GPT_4O_2024_05_13 = 71,
    /**
     * 聊天 GPT 4O 2024 08 06
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4O_2024_08_06 = 109;
     */
    MODEL_CHAT_GPT_4O_2024_08_06 = 109,
    /**
     * 聊天 GPT 4O Mini 2024 07 18
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4O_MINI_2024_07_18 = 113;
     */
    MODEL_CHAT_GPT_4O_MINI_2024_07_18 = 113,
    /**
     * 聊天 GPT 4 1 2025 04 14
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4_1_2025_04_14 = 259;
     */
    MODEL_CHAT_GPT_4_1_2025_04_14 = 259,
    /**
     * 聊天 GPT 4 1 Mini 2025 04 14
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4_1_MINI_2025_04_14 = 260;
     */
    MODEL_CHAT_GPT_4_1_MINI_2025_04_14 = 260,
    /**
     * 聊天 GPT 4 1 Nano 2025 04 14
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4_1_NANO_2025_04_14 = 261;
     */
    MODEL_CHAT_GPT_4_1_NANO_2025_04_14 = 261,
    /**
     * 聊天 O1 预览
     *
     * @generated from protobuf enum value: MODEL_CHAT_O1_PREVIEW = 117;
     */
    MODEL_CHAT_O1_PREVIEW = 117,
    /**
     * 聊天 O1 Mini
     *
     * @generated from protobuf enum value: MODEL_CHAT_O1_MINI = 118;
     */
    MODEL_CHAT_O1_MINI = 118,
    /**
     * 聊天 O1
     *
     * @generated from protobuf enum value: MODEL_CHAT_O1 = 170;
     */
    MODEL_CHAT_O1 = 170,
    /**
     * 聊天 O3 Mini
     *
     * @generated from protobuf enum value: MODEL_CHAT_O3_MINI = 207;
     */
    MODEL_CHAT_O3_MINI = 207,
    /**
     * 聊天 O3 Mini Low
     *
     * @generated from protobuf enum value: MODEL_CHAT_O3_MINI_LOW = 213;
     */
    MODEL_CHAT_O3_MINI_LOW = 213,
    /**
     * 聊天 O3 Mini High
     *
     * @generated from protobuf enum value: MODEL_CHAT_O3_MINI_HIGH = 214;
     */
    MODEL_CHAT_O3_MINI_HIGH = 214,
    /**
     * 聊天 O3
     *
     * @generated from protobuf enum value: MODEL_CHAT_O3 = 218;
     */
    MODEL_CHAT_O3 = 218,
    /**
     * 聊天 O3 Low
     *
     * @generated from protobuf enum value: MODEL_CHAT_O3_LOW = 262;
     */
    MODEL_CHAT_O3_LOW = 262,
    /**
     * 聊天 O3 High
     *
     * @generated from protobuf enum value: MODEL_CHAT_O3_HIGH = 263;
     */
    MODEL_CHAT_O3_HIGH = 263,
    /**
     * 聊天 O4 Mini
     *
     * @generated from protobuf enum value: MODEL_CHAT_O4_MINI = 264;
     */
    MODEL_CHAT_O4_MINI = 264,
    /**
     * 聊天 O4 Mini Low
     *
     * @generated from protobuf enum value: MODEL_CHAT_O4_MINI_LOW = 265;
     */
    MODEL_CHAT_O4_MINI_LOW = 265,
    /**
     * 聊天 O4 Mini High
     *
     * @generated from protobuf enum value: MODEL_CHAT_O4_MINI_HIGH = 266;
     */
    MODEL_CHAT_O4_MINI_HIGH = 266,
    /**
     * 聊天 GPT 4 5
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_4_5 = 228;
     */
    MODEL_CHAT_GPT_4_5 = 228,
    /**
     * Google Gemini 1.0 Pro
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_1_0_PRO = 61;
     */
    MODEL_GOOGLE_GEMINI_1_0_PRO = 61,
    /**
     * Google Gemini 1.5 Pro
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_1_5_PRO = 62;
     */
    MODEL_GOOGLE_GEMINI_1_5_PRO = 62,
    /**
     * Google Gemini Exp 1206
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_EXP_1206 = 183;
     */
    MODEL_GOOGLE_GEMINI_EXP_1206 = 183,
    /**
     * Google Gemini 2.0 Flash
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_0_FLASH = 184;
     */
    MODEL_GOOGLE_GEMINI_2_0_FLASH = 184,
    /**
     * Google Gemini 2.5 Pro
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_PRO = 246;
     */
    MODEL_GOOGLE_GEMINI_2_5_PRO = 246,
    /**
     * Google Gemini 2.5 Flash Preview 04 17
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_04_17 = 272;
     */
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_04_17 = 272,
    /**
     * Claude 3 Opus 20240229
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_OPUS_20240229 = 63;
     */
    MODEL_CLAUDE_3_OPUS_20240229 = 63,
    /**
     * Claude 3 Sonnet 20240229
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_SONNET_20240229 = 64;
     */
    MODEL_CLAUDE_3_SONNET_20240229 = 64,
    /**
     * Claude 3.5 Sonnet 20240620
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_5_SONNET_20240620 = 80;
     */
    MODEL_CLAUDE_3_5_SONNET_20240620 = 80,
    /**
     * Claude 3.5 Haiku 20241022
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_5_HAIKU_20241022 = 171;
     */
    MODEL_CLAUDE_3_5_HAIKU_20241022 = 171,
    /**
     * Claude 3.5 Sonnet 20241022
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_5_SONNET_20241022 = 166;
     */
    MODEL_CLAUDE_3_5_SONNET_20241022 = 166,
    /**
     * Claude 3 Haiku 20240307
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_HAIKU_20240307 = 172;
     */
    MODEL_CLAUDE_3_HAIKU_20240307 = 172,
    /**
     * Claude 3.7 Sonnet 20250219
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_7_SONNET_20250219 = 226;
     */
    MODEL_CLAUDE_3_7_SONNET_20250219 = 226,
    /**
     * Claude 3.7 Sonnet 20250219 Thinking
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_3_7_SONNET_20250219_THINKING = 227;
     */
    MODEL_CLAUDE_3_7_SONNET_20250219_THINKING = 227,
    /**
     * Claude 4.0 Opus - update 2025-06-21 windsurf 1.11.0
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_OPUS = 290;
     */
    MODEL_CLAUDE_4_OPUS = 290,
    /**
     * Claude 4.0 Sonnet - update 2025-06-21 windsurf 1.11.0
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_SONNET = 281;
     */
    MODEL_CLAUDE_4_SONNET = 281,
    /**
     * 新增模型 - update 2025-06-21 windsurf 1.11.0
     *
     * Google Gemini 2.5 Flash
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH = 312;
     */
    MODEL_GOOGLE_GEMINI_2_5_FLASH = 312,
    /**
     * Google Gemini 2.5 Flash Thinking
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH_THINKING = 313;
     */
    MODEL_GOOGLE_GEMINI_2_5_FLASH_THINKING = 313,
    /**
     * Google Gemini 2.5 Flash Preview 05-20
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20 = 275;
     */
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20 = 275,
    /**
     * Google Gemini 2.5 Flash Preview 05-20 Thinking
     *
     * @generated from protobuf enum value: MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING = 276;
     */
    MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING = 276,
    /**
     * OpenAI O3 Pro 2025-06-10
     *
     * @generated from protobuf enum value: MODEL_O3_PRO_2025_06_10 = 294;
     */
    MODEL_O3_PRO_2025_06_10 = 294,
    /**
     * OpenAI O3 Pro 2025-06-10 Low
     *
     * @generated from protobuf enum value: MODEL_O3_PRO_2025_06_10_LOW = 295;
     */
    MODEL_O3_PRO_2025_06_10_LOW = 295,
    /**
     * OpenAI O3 Pro 2025-06-10 High
     *
     * @generated from protobuf enum value: MODEL_O3_PRO_2025_06_10_HIGH = 296;
     */
    MODEL_O3_PRO_2025_06_10_HIGH = 296,
    /**
     * 新增模型 - update 2025-06-21 windsurf 1.12.1
     *
     * Kimi K2
     *
     * @generated from protobuf enum value: MODEL_KIMI_K2 = 323;
     */
    MODEL_KIMI_K2 = 323,
    /**
     * Qwen 3 235B Instruct
     *
     * @generated from protobuf enum value: MODEL_QWEN_3_235B_INSTRUCT = 324;
     */
    MODEL_QWEN_3_235B_INSTRUCT = 324,
    /**
     * Qwen 3 Coder 480B Instruct
     *
     * @generated from protobuf enum value: MODEL_QWEN_3_CODER_480B_INSTRUCT = 325;
     */
    MODEL_QWEN_3_CODER_480B_INSTRUCT = 325,
    /**
     * GPT OSS 120B
     *
     * @generated from protobuf enum value: MODEL_GPT_OSS_120B = 326;
     */
    MODEL_GPT_OSS_120B = 326,
    /**
     * Qwen 3 Coder 480B Instruct Fast
     *
     * @generated from protobuf enum value: MODEL_QWEN_3_CODER_480B_INSTRUCT_FAST = 327;
     */
    MODEL_QWEN_3_CODER_480B_INSTRUCT_FAST = 327,
    /**
     * Claude 4.1 Opus
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_1_OPUS = 328;
     */
    MODEL_CLAUDE_4_1_OPUS = 328,
    /**
     * Claude 4.1 Opus Thinking
     *
     * @generated from protobuf enum value: MODEL_CLAUDE_4_1_OPUS_THINKING = 329;
     */
    MODEL_CLAUDE_4_1_OPUS_THINKING = 329,
    /**
     * GPT-5 Nano
     *
     * @generated from protobuf enum value: MODEL_GPT_5_NANO = 337;
     */
    MODEL_GPT_5_NANO = 337,
    /**
     * GPT-5 Minimal
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5_MINIMAL = 338;
     */
    MODEL_CHAT_GPT_5_MINIMAL = 338,
    /**
     * GPT-5 Low Reasoning
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5_LOW = 339;
     */
    MODEL_CHAT_GPT_5_LOW = 339,
    /**
     * GPT-5 Standard
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5 = 340;
     */
    MODEL_CHAT_GPT_5 = 340,
    /**
     * GPT-5 High Reasoning
     *
     * @generated from protobuf enum value: MODEL_CHAT_GPT_5_HIGH = 341;
     */
    MODEL_CHAT_GPT_5_HIGH = 341,
    /**
     * TogetherAI Text Embedding M2 BERT
     *
     * @generated from protobuf enum value: MODEL_TOGETHERAI_TEXT_EMBEDDING_M2_BERT = 81;
     */
    MODEL_TOGETHERAI_TEXT_EMBEDDING_M2_BERT = 81,
    /**
     * TogetherAI Llama 3.1 8B Instruct
     *
     * @generated from protobuf enum value: MODEL_TOGETHERAI_LLAMA_3_1_8B_INSTRUCT = 165;
     */
    MODEL_TOGETHERAI_LLAMA_3_1_8B_INSTRUCT = 165,
    /**
     * Hugging Face Text Embedding M2 BERT
     *
     * @generated from protobuf enum value: MODEL_HUGGING_FACE_TEXT_EMBEDDING_M2_BERT = 82;
     */
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_M2_BERT = 82,
    /**
     * Hugging Face Text Embedding UAE Code
     *
     * @generated from protobuf enum value: MODEL_HUGGING_FACE_TEXT_EMBEDDING_UAE_CODE = 83;
     */
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_UAE_CODE = 83,
    /**
     * Hugging Face Text Embedding BGE
     *
     * @generated from protobuf enum value: MODEL_HUGGING_FACE_TEXT_EMBEDDING_BGE = 84;
     */
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_BGE = 84,
    /**
     * Hugging Face Text Embedding Blade
     *
     * @generated from protobuf enum value: MODEL_HUGGING_FACE_TEXT_EMBEDDING_BLADE = 85;
     */
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_BLADE = 85,
    /**
     * Hugging Face Text Embedding Arctic Large
     *
     * @generated from protobuf enum value: MODEL_HUGGING_FACE_TEXT_EMBEDDING_ARCTIC_LARGE = 86;
     */
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_ARCTIC_LARGE = 86,
    /**
     * Hugging Face Text Embedding E5 Base
     *
     * @generated from protobuf enum value: MODEL_HUGGING_FACE_TEXT_EMBEDDING_E5_BASE = 87;
     */
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_E5_BASE = 87,
    /**
     * Hugging Face Text Embedding MXBAI
     *
     * @generated from protobuf enum value: MODEL_HUGGING_FACE_TEXT_EMBEDDING_MXBAI = 88;
     */
    MODEL_HUGGING_FACE_TEXT_EMBEDDING_MXBAI = 88,
    /**
     * Llama 3.1 8B Instruct
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_1_8B_INSTRUCT = 106;
     */
    MODEL_LLAMA_3_1_8B_INSTRUCT = 106,
    /**
     * Llama 3.1 70B Instruct
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_1_70B_INSTRUCT = 107;
     */
    MODEL_LLAMA_3_1_70B_INSTRUCT = 107,
    /**
     * Llama 3.1 405B Instruct
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_1_405B_INSTRUCT = 105;
     */
    MODEL_LLAMA_3_1_405B_INSTRUCT = 105,
    /**
     * Llama 3.3 70B Instruct
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_3_70B_INSTRUCT = 208;
     */
    MODEL_LLAMA_3_3_70B_INSTRUCT = 208,
    /**
     * Llama 3.3 70B Instruct R1
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_3_70B_INSTRUCT_R1 = 209;
     */
    MODEL_LLAMA_3_3_70B_INSTRUCT_R1 = 209,
    /**
     * Llama 3.1 70B Instruct Long Context
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT = 116;
     */
    MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT = 116,
    /**
     * Llama 3.1 8B Hermes 3
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_1_8B_HERMES_3 = 176;
     */
    MODEL_LLAMA_3_1_8B_HERMES_3 = 176,
    /**
     * Llama 3.1 70B Hermes 3
     *
     * @generated from protobuf enum value: MODEL_LLAMA_3_1_70B_HERMES_3 = 177;
     */
    MODEL_LLAMA_3_1_70B_HERMES_3 = 177,
    /**
     * Qwen 2.5 7B Instruct
     *
     * @generated from protobuf enum value: MODEL_QWEN_2_5_7B_INSTRUCT = 178;
     */
    MODEL_QWEN_2_5_7B_INSTRUCT = 178,
    /**
     * Qwen 2.5 32B Instruct
     *
     * @generated from protobuf enum value: MODEL_QWEN_2_5_32B_INSTRUCT = 179;
     */
    MODEL_QWEN_2_5_32B_INSTRUCT = 179,
    /**
     * Qwen 2.5 72B Instruct
     *
     * @generated from protobuf enum value: MODEL_QWEN_2_5_72B_INSTRUCT = 180;
     */
    MODEL_QWEN_2_5_72B_INSTRUCT = 180,
    /**
     * Qwen 2.5 32B Instruct R1
     *
     * @generated from protobuf enum value: MODEL_QWEN_2_5_32B_INSTRUCT_R1 = 224;
     */
    MODEL_QWEN_2_5_32B_INSTRUCT_R1 = 224,
    /**
     * Nomic Text Embedding V1
     *
     * @generated from protobuf enum value: MODEL_NOMIC_TEXT_EMBEDDING_V1 = 89;
     */
    MODEL_NOMIC_TEXT_EMBEDDING_V1 = 89,
    /**
     * Nomic Text Embedding V1.5
     *
     * @generated from protobuf enum value: MODEL_NOMIC_TEXT_EMBEDDING_V1_5 = 90;
     */
    MODEL_NOMIC_TEXT_EMBEDDING_V1_5 = 90,
    /**
     * MISTRAL 7B
     *
     * @generated from protobuf enum value: MODEL_MISTRAL_7B = 77;
     */
    MODEL_MISTRAL_7B = 77,
    /**
     * Salesforce Embedding 2R
     *
     * @generated from protobuf enum value: MODEL_SALESFORCE_EMBEDDING_2R = 99;
     */
    MODEL_SALESFORCE_EMBEDDING_2R = 99,
    /**
     * Custom VLLM
     *
     * @generated from protobuf enum value: MODEL_CUSTOM_VLLM = 182;
     */
    MODEL_CUSTOM_VLLM = 182,
    /**
     * TEI BGE M3
     *
     * @generated from protobuf enum value: MODEL_TEI_BGE_M3 = 92;
     */
    MODEL_TEI_BGE_M3 = 92,
    /**
     * TEI NOMIC Embed Text V1
     *
     * @generated from protobuf enum value: MODEL_TEI_NOMIC_EMBED_TEXT_V1 = 93;
     */
    MODEL_TEI_NOMIC_EMBED_TEXT_V1 = 93,
    /**
     * TEI Intfloat E5 Large Instruct
     *
     * @generated from protobuf enum value: MODEL_TEI_INTFLOAT_E5_LARGE_INSTRUCT = 94;
     */
    MODEL_TEI_INTFLOAT_E5_LARGE_INSTRUCT = 94,
    /**
     * TEI Snowflake Arctic Embed L
     *
     * @generated from protobuf enum value: MODEL_TEI_SNOWFLAKE_ARCTIC_EMBED_L = 95;
     */
    MODEL_TEI_SNOWFLAKE_ARCTIC_EMBED_L = 95,
    /**
     * TEI UAE Code Large V1
     *
     * @generated from protobuf enum value: MODEL_TEI_UAE_CODE_LARGE_V1 = 96;
     */
    MODEL_TEI_UAE_CODE_LARGE_V1 = 96,
    /**
     * TEI B1ADE
     *
     * @generated from protobuf enum value: MODEL_TEI_B1ADE = 97;
     */
    MODEL_TEI_B1ADE = 97,
    /**
     * TEI Whereisai UAE Large V1
     *
     * @generated from protobuf enum value: MODEL_TEI_WHEREISAI_UAE_LARGE_V1 = 98;
     */
    MODEL_TEI_WHEREISAI_UAE_LARGE_V1 = 98,
    /**
     * TEI Whereisai UAE Code Large V1
     *
     * @generated from protobuf enum value: MODEL_TEI_WHEREISAI_UAE_CODE_LARGE_V1 = 100;
     */
    MODEL_TEI_WHEREISAI_UAE_CODE_LARGE_V1 = 100,
    /**
     * OpenAI Compatible
     *
     * @generated from protobuf enum value: MODEL_OPENAI_COMPATIBLE = 200;
     */
    MODEL_OPENAI_COMPATIBLE = 200,
    /**
     * Anthropic Compatible
     *
     * @generated from protobuf enum value: MODEL_ANTHROPIC_COMPATIBLE = 201;
     */
    MODEL_ANTHROPIC_COMPATIBLE = 201,
    /**
     * Vertex Compatible
     *
     * @generated from protobuf enum value: MODEL_VERTEX_COMPATIBLE = 202;
     */
    MODEL_VERTEX_COMPATIBLE = 202,
    /**
     * Bedrock Compatible
     *
     * @generated from protobuf enum value: MODEL_BEDROCK_COMPATIBLE = 203;
     */
    MODEL_BEDROCK_COMPATIBLE = 203,
    /**
     * Azure Compatible
     *
     * @generated from protobuf enum value: MODEL_AZURE_COMPATIBLE = 204;
     */
    MODEL_AZURE_COMPATIBLE = 204,
    /**
     * Deepseek V3
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_V3 = 205;
     */
    MODEL_DEEPSEEK_V3 = 205,
    /**
     * Deepseek R1
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_R1 = 206;
     */
    MODEL_DEEPSEEK_R1 = 206,
    /**
     * Deepseek R1 Slow
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_R1_SLOW = 215;
     */
    MODEL_DEEPSEEK_R1_SLOW = 215,
    /**
     * Deepseek R1 Fast
     *
     * @generated from protobuf enum value: MODEL_DEEPSEEK_R1_FAST = 216;
     */
    MODEL_DEEPSEEK_R1_FAST = 216,
    /**
     * Custom Open Router
     *
     * @generated from protobuf enum value: MODEL_CUSTOM_OPEN_ROUTER = 185;
     */
    MODEL_CUSTOM_OPEN_ROUTER = 185,
    /**
     * XAI Grok 2
     *
     * @generated from protobuf enum value: MODEL_XAI_GROK_2 = 212;
     */
    MODEL_XAI_GROK_2 = 212,
    /**
     * XAI Grok 3
     *
     * @generated from protobuf enum value: MODEL_XAI_GROK_3 = 217;
     */
    MODEL_XAI_GROK_3 = 217,
    /**
     * XAI Grok 3 Mini Reasoning
     *
     * @generated from protobuf enum value: MODEL_XAI_GROK_3_MINI_REASONING = 234;
     */
    MODEL_XAI_GROK_3_MINI_REASONING = 234,
    /**
     * Private 1
     *
     * @generated from protobuf enum value: MODEL_PRIVATE_1 = 219;
     */
    MODEL_PRIVATE_1 = 219,
    /**
     * Private 2
     *
     * @generated from protobuf enum value: MODEL_PRIVATE_2 = 220;
     */
    MODEL_PRIVATE_2 = 220,
    /**
     * Private 3
     *
     * @generated from protobuf enum value: MODEL_PRIVATE_3 = 221;
     */
    MODEL_PRIVATE_3 = 221,
    /**
     * Private 4
     *
     * @generated from protobuf enum value: MODEL_PRIVATE_4 = 222;
     */
    MODEL_PRIVATE_4 = 222,
    /**
     * Private 5
     *
     * @generated from protobuf enum value: MODEL_PRIVATE_5 = 223;
     */
    MODEL_PRIVATE_5 = 223
}
/**
 * @generated from protobuf enum exa.codeium_common_pb.Alias
 */
export enum Alias {
    /**
     * 未指定别名
     *
     * @generated from protobuf enum value: MODEL_ALIAS_UNSPECIFIED = 0;
     */
    MODEL_ALIAS_UNSPECIFIED = 0,
    /**
     * Cascade Base
     *
     * @generated from protobuf enum value: MODEL_ALIAS_CASCADE_BASE = 1;
     */
    MODEL_ALIAS_CASCADE_BASE = 1,
    /**
     * Vista
     *
     * @generated from protobuf enum value: MODEL_ALIAS_VISTA = 3;
     */
    MODEL_ALIAS_VISTA = 3,
    /**
     * Shamu
     *
     * @generated from protobuf enum value: MODEL_ALIAS_SHAMU = 4;
     */
    MODEL_ALIAS_SHAMU = 4,
    /**
     * SWE 1
     *
     * @generated from protobuf enum value: MODEL_ALIAS_SWE_1 = 5;
     */
    MODEL_ALIAS_SWE_1 = 5,
    /**
     * SWE 1 Lite
     *
     * @generated from protobuf enum value: MODEL_ALIAS_SWE_1_LITE = 6;
     */
    MODEL_ALIAS_SWE_1_LITE = 6
}
/**
 * @generated from protobuf enum exa.codeium_common_pb.Provider
 */
export enum Provider {
    /**
     * 未指定提供商
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_UNSPECIFIED = 0;
     */
    MODEL_PROVIDER_UNSPECIFIED = 0,
    /**
     * Windsurf
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_WINDSURF = 1;
     */
    MODEL_PROVIDER_WINDSURF = 1,
    /**
     * OpenAI
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_OPENAI = 2;
     */
    MODEL_PROVIDER_OPENAI = 2,
    /**
     * Anthropic
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_ANTHROPIC = 3;
     */
    MODEL_PROVIDER_ANTHROPIC = 3,
    /**
     * Google
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_GOOGLE = 4;
     */
    MODEL_PROVIDER_GOOGLE = 4,
    /**
     * XAI
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_XAI = 5;
     */
    MODEL_PROVIDER_XAI = 5,
    /**
     * DeepSeek
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_DEEPSEEK = 6;
     */
    MODEL_PROVIDER_DEEPSEEK = 6,
    /**
     * 新增提供商 - update 2025-06-21 windsurf 1.12.1
     *
     * Qwen
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_QWEN = 7;
     */
    MODEL_PROVIDER_QWEN = 7,
    /**
     * Kimi
     *
     * @generated from protobuf enum value: MODEL_PROVIDER_KIMI = 8;
     */
    MODEL_PROVIDER_KIMI = 8
}
/**
 * @generated from protobuf enum exa.codeium_common_pb.Tier
 */
export enum Tier {
    /**
     * 未指定等级
     *
     * @generated from protobuf enum value: MODEL_TIER_UNSPECIFIED = 0;
     */
    MODEL_TIER_UNSPECIFIED = 0,
    /**
     * Teams
     *
     * @generated from protobuf enum value: MODEL_TIER_TEAMS = 1;
     */
    MODEL_TIER_TEAMS = 1,
    /**
     * Pro
     *
     * @generated from protobuf enum value: MODEL_TIER_PRO = 2;
     */
    MODEL_TIER_PRO = 2,
    /**
     * Trial
     *
     * @generated from protobuf enum value: MODEL_TIER_TRIAL = 9;
     */
    MODEL_TIER_TRIAL = 9,
    /**
     * Enterprise SaaS
     *
     * @generated from protobuf enum value: MODEL_TIER_ENTERPRISE_SAAS = 3;
     */
    MODEL_TIER_ENTERPRISE_SAAS = 3,
    /**
     * Hybrid
     *
     * @generated from protobuf enum value: MODEL_TIER_HYBRID = 4;
     */
    MODEL_TIER_HYBRID = 4,
    /**
     * Enterprise Self-Hosted
     *
     * @generated from protobuf enum value: MODEL_TIER_ENTERPRISE_SELF_HOSTED = 5;
     */
    MODEL_TIER_ENTERPRISE_SELF_HOSTED = 5,
    /**
     * Waitlist Pro
     *
     * @generated from protobuf enum value: MODEL_TIER_WAITLIST_PRO = 6;
     */
    MODEL_TIER_WAITLIST_PRO = 6,
    /**
     * Teams Ultimate
     *
     * @generated from protobuf enum value: MODEL_TIER_TEAMS_ULTIMATE = 7;
     */
    MODEL_TIER_TEAMS_ULTIMATE = 7,
    /**
     * Pro Ultimate
     *
     * @generated from protobuf enum value: MODEL_TIER_PRO_ULTIMATE = 8;
     */
    MODEL_TIER_PRO_ULTIMATE = 8,
    /**
     * Enterprise Self-Serve
     *
     * @generated from protobuf enum value: MODEL_TIER_ENTERPRISE_SELF_SERVE = 10;
     */
    MODEL_TIER_ENTERPRISE_SELF_SERVE = 10
}
// @generated message type with reflection information, may provide speed optimized methods
class GetCascadeModelConfigsResponse$Type extends MessageType<GetCascadeModelConfigsResponse> {
    constructor() {
        super("exa.codeium_common_pb.GetCascadeModelConfigsResponse", [
            { no: 1, name: "client_model_configs", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ClientModelConfig }
        ]);
    }
    create(value?: PartialMessage<GetCascadeModelConfigsResponse>): GetCascadeModelConfigsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.clientModelConfigs = [];
        if (value !== undefined)
            reflectionMergePartial<GetCascadeModelConfigsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetCascadeModelConfigsResponse): GetCascadeModelConfigsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated exa.codeium_common_pb.ClientModelConfig client_model_configs */ 1:
                    message.clientModelConfigs.push(ClientModelConfig.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetCascadeModelConfigsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated exa.codeium_common_pb.ClientModelConfig client_model_configs = 1; */
        for (let i = 0; i < message.clientModelConfigs.length; i++)
            ClientModelConfig.internalBinaryWrite(message.clientModelConfigs[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.codeium_common_pb.GetCascadeModelConfigsResponse
 */
export const GetCascadeModelConfigsResponse = new GetCascadeModelConfigsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class GetCommandModelConfigsResponse$Type extends MessageType<GetCommandModelConfigsResponse> {
    constructor() {
        super("exa.codeium_common_pb.GetCommandModelConfigsResponse", [
            { no: 1, name: "client_model_configs", kind: "message", repeat: 2 /*RepeatType.UNPACKED*/, T: () => ClientModelConfig }
        ]);
    }
    create(value?: PartialMessage<GetCommandModelConfigsResponse>): GetCommandModelConfigsResponse {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.clientModelConfigs = [];
        if (value !== undefined)
            reflectionMergePartial<GetCommandModelConfigsResponse>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: GetCommandModelConfigsResponse): GetCommandModelConfigsResponse {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* repeated exa.codeium_common_pb.ClientModelConfig client_model_configs */ 1:
                    message.clientModelConfigs.push(ClientModelConfig.internalBinaryRead(reader, reader.uint32(), options));
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: GetCommandModelConfigsResponse, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* repeated exa.codeium_common_pb.ClientModelConfig client_model_configs = 1; */
        for (let i = 0; i < message.clientModelConfigs.length; i++)
            ClientModelConfig.internalBinaryWrite(message.clientModelConfigs[i], writer.tag(1, WireType.LengthDelimited).fork(), options).join();
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.codeium_common_pb.GetCommandModelConfigsResponse
 */
export const GetCommandModelConfigsResponse = new GetCommandModelConfigsResponse$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ClientModelConfig$Type extends MessageType<ClientModelConfig> {
    constructor() {
        super("exa.codeium_common_pb.ClientModelConfig", [
            { no: 1, name: "label", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 2, name: "model_or_alias", kind: "message", T: () => ModelOrAlias },
            { no: 3, name: "credit_multiplier", kind: "scalar", T: 7 /*ScalarType.FIXED32*/ },
            { no: 4, name: "disabled", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 5, name: "supports_images", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 6, name: "supports_legacy", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 7, name: "is_premium", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 8, name: "beta_warning_message", kind: "scalar", T: 9 /*ScalarType.STRING*/ },
            { no: 9, name: "is_beta", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 10, name: "provider", kind: "enum", T: () => ["exa.codeium_common_pb.Provider", Provider] },
            { no: 11, name: "is_recommended", kind: "scalar", T: 8 /*ScalarType.BOOL*/ },
            { no: 12, name: "allowed_tiers", kind: "enum", repeat: 1 /*RepeatType.PACKED*/, T: () => ["exa.codeium_common_pb.Tier", Tier] }
        ]);
    }
    create(value?: PartialMessage<ClientModelConfig>): ClientModelConfig {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.label = "";
        message.creditMultiplier = 0;
        message.disabled = false;
        message.supportsImages = false;
        message.supportsLegacy = false;
        message.isPremium = false;
        message.betaWarningMessage = "";
        message.isBeta = false;
        message.provider = 0;
        message.isRecommended = false;
        message.allowedTiers = [];
        if (value !== undefined)
            reflectionMergePartial<ClientModelConfig>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ClientModelConfig): ClientModelConfig {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* string label */ 1:
                    message.label = reader.string();
                    break;
                case /* exa.codeium_common_pb.ModelOrAlias model_or_alias */ 2:
                    message.modelOrAlias = ModelOrAlias.internalBinaryRead(reader, reader.uint32(), options, message.modelOrAlias);
                    break;
                case /* fixed32 credit_multiplier */ 3:
                    message.creditMultiplier = reader.fixed32();
                    break;
                case /* bool disabled */ 4:
                    message.disabled = reader.bool();
                    break;
                case /* bool supports_images */ 5:
                    message.supportsImages = reader.bool();
                    break;
                case /* bool supports_legacy */ 6:
                    message.supportsLegacy = reader.bool();
                    break;
                case /* bool is_premium */ 7:
                    message.isPremium = reader.bool();
                    break;
                case /* string beta_warning_message */ 8:
                    message.betaWarningMessage = reader.string();
                    break;
                case /* bool is_beta */ 9:
                    message.isBeta = reader.bool();
                    break;
                case /* exa.codeium_common_pb.Provider provider */ 10:
                    message.provider = reader.int32();
                    break;
                case /* bool is_recommended */ 11:
                    message.isRecommended = reader.bool();
                    break;
                case /* repeated exa.codeium_common_pb.Tier allowed_tiers */ 12:
                    if (wireType === WireType.LengthDelimited)
                        for (let e = reader.int32() + reader.pos; reader.pos < e;)
                            message.allowedTiers.push(reader.int32());
                    else
                        message.allowedTiers.push(reader.int32());
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ClientModelConfig, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* string label = 1; */
        if (message.label !== "")
            writer.tag(1, WireType.LengthDelimited).string(message.label);
        /* exa.codeium_common_pb.ModelOrAlias model_or_alias = 2; */
        if (message.modelOrAlias)
            ModelOrAlias.internalBinaryWrite(message.modelOrAlias, writer.tag(2, WireType.LengthDelimited).fork(), options).join();
        /* fixed32 credit_multiplier = 3; */
        if (message.creditMultiplier !== 0)
            writer.tag(3, WireType.Bit32).fixed32(message.creditMultiplier);
        /* bool disabled = 4; */
        if (message.disabled !== false)
            writer.tag(4, WireType.Varint).bool(message.disabled);
        /* bool supports_images = 5; */
        if (message.supportsImages !== false)
            writer.tag(5, WireType.Varint).bool(message.supportsImages);
        /* bool supports_legacy = 6; */
        if (message.supportsLegacy !== false)
            writer.tag(6, WireType.Varint).bool(message.supportsLegacy);
        /* bool is_premium = 7; */
        if (message.isPremium !== false)
            writer.tag(7, WireType.Varint).bool(message.isPremium);
        /* string beta_warning_message = 8; */
        if (message.betaWarningMessage !== "")
            writer.tag(8, WireType.LengthDelimited).string(message.betaWarningMessage);
        /* bool is_beta = 9; */
        if (message.isBeta !== false)
            writer.tag(9, WireType.Varint).bool(message.isBeta);
        /* exa.codeium_common_pb.Provider provider = 10; */
        if (message.provider !== 0)
            writer.tag(10, WireType.Varint).int32(message.provider);
        /* bool is_recommended = 11; */
        if (message.isRecommended !== false)
            writer.tag(11, WireType.Varint).bool(message.isRecommended);
        /* repeated exa.codeium_common_pb.Tier allowed_tiers = 12; */
        if (message.allowedTiers.length) {
            writer.tag(12, WireType.LengthDelimited).fork();
            for (let i = 0; i < message.allowedTiers.length; i++)
                writer.int32(message.allowedTiers[i]);
            writer.join();
        }
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.codeium_common_pb.ClientModelConfig
 */
export const ClientModelConfig = new ClientModelConfig$Type();
// @generated message type with reflection information, may provide speed optimized methods
class ModelOrAlias$Type extends MessageType<ModelOrAlias> {
    constructor() {
        super("exa.codeium_common_pb.ModelOrAlias", [
            { no: 1, name: "model", kind: "enum", oneof: "choice", T: () => ["exa.codeium_common_pb.Model", Model] },
            { no: 2, name: "alias", kind: "enum", oneof: "choice", T: () => ["exa.codeium_common_pb.Alias", Alias] }
        ]);
    }
    create(value?: PartialMessage<ModelOrAlias>): ModelOrAlias {
        const message = globalThis.Object.create((this.messagePrototype!));
        message.choice = { oneofKind: undefined };
        if (value !== undefined)
            reflectionMergePartial<ModelOrAlias>(this, message, value);
        return message;
    }
    internalBinaryRead(reader: IBinaryReader, length: number, options: BinaryReadOptions, target?: ModelOrAlias): ModelOrAlias {
        let message = target ?? this.create(), end = reader.pos + length;
        while (reader.pos < end) {
            let [fieldNo, wireType] = reader.tag();
            switch (fieldNo) {
                case /* exa.codeium_common_pb.Model model */ 1:
                    message.choice = {
                        oneofKind: "model",
                        model: reader.int32()
                    };
                    break;
                case /* exa.codeium_common_pb.Alias alias */ 2:
                    message.choice = {
                        oneofKind: "alias",
                        alias: reader.int32()
                    };
                    break;
                default:
                    let u = options.readUnknownField;
                    if (u === "throw")
                        throw new globalThis.Error(`Unknown field ${fieldNo} (wire type ${wireType}) for ${this.typeName}`);
                    let d = reader.skip(wireType);
                    if (u !== false)
                        (u === true ? UnknownFieldHandler.onRead : u)(this.typeName, message, fieldNo, wireType, d);
            }
        }
        return message;
    }
    internalBinaryWrite(message: ModelOrAlias, writer: IBinaryWriter, options: BinaryWriteOptions): IBinaryWriter {
        /* exa.codeium_common_pb.Model model = 1; */
        if (message.choice.oneofKind === "model")
            writer.tag(1, WireType.Varint).int32((message.choice as { oneofKind: "model"; model: Model }).model);
        /* exa.codeium_common_pb.Alias alias = 2; */
        if (message.choice.oneofKind === "alias")
            writer.tag(2, WireType.Varint).int32((message.choice as { oneofKind: "alias"; alias: Alias }).alias);
        let u = options.writeUnknownFields;
        if (u !== false)
            (u == true ? UnknownFieldHandler.onWrite : u)(this.typeName, message, writer);
        return writer;
    }
}
/**
 * @generated MessageType for protobuf message exa.codeium_common_pb.ModelOrAlias
 */
export const ModelOrAlias = new ModelOrAlias$Type();
